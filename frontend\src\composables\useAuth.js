import { ref, computed } from 'vue'

// Global authentication state
const isAuthenticated = ref(false)
const currentUser = ref(null)

export function useAuth() {
  const login = async (username, password) => {
    try {
      // Hash the password using SHA-512 (similar to existing logic)
      const encoder = new TextEncoder()
      const data = encoder.encode(password)
      const hashBuffer = await crypto.subtle.digest('SHA-512', data)
      const hashArray = Array.from(new Uint8Array(hashBuffer))
      const hashedPassword = hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
      
      // For now, we'll simulate a successful login
      // In a real application, you would make an API call here
      console.log('Login attempt:', { username, hashedPassword })
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 500))
      
      // For demo purposes, accept any non-empty username/password
      if (username.trim() && password.trim()) {
        isAuthenticated.value = true
        currentUser.value = { username, id: 1 }
        
        // Store authentication state in localStorage
        localStorage.setItem('isAuthenticated', 'true')
        localStorage.setItem('currentUser', JSON.stringify(currentUser.value))
        
        return { success: true }
      } else {
        return { success: false, error: 'Username and password are required' }
      }
    } catch (error) {
      console.error('Login error:', error)
      return { success: false, error: 'Login failed. Please try again.' }
    }
  }

  const logout = () => {
    isAuthenticated.value = false
    currentUser.value = null
    localStorage.removeItem('isAuthenticated')
    localStorage.removeItem('currentUser')
  }

  const checkAuthState = () => {
    const storedAuth = localStorage.getItem('isAuthenticated')
    const storedUser = localStorage.getItem('currentUser')
    
    if (storedAuth === 'true' && storedUser) {
      isAuthenticated.value = true
      currentUser.value = JSON.parse(storedUser)
    }
  }

  return {
    isAuthenticated: computed(() => isAuthenticated.value),
    currentUser: computed(() => currentUser.value),
    login,
    logout,
    checkAuthState
  }
}
