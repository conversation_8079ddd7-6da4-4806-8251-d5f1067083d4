<script>
import { useAuth } from '../composables/useAuth.js'
import { useRouter } from 'vue-router'
import jsSHA from 'jssha'

export default {
  setup() {
    const { login } = useAuth()
    const router = useRouter()
    return { login, router }
  },
  data() {
    return {
      username: '',
      password: '',
      isLoading: false,
      errorMessage: ''
    }
  },
  methods: {
    async tryLogin() {
      // Keep the original hashing logic
      var pwdObj = this.password;
      var hashObj = new jsSHA("SHA-512", "TEXT", {numRounds: 1});
      hashObj.update(pwdObj);
      var hash = hashObj.getHash("HEX");
      pwdObj = hash;

      // Keep the original alert for debugging
      alert("username: " + this.username + "\npaswword: " + pwdObj);

      // Add authentication logic
      if (!this.username.trim() || !this.password.trim()) {
        this.errorMessage = 'Please enter both username and password'
        return
      }

      this.isLoading = true
      this.errorMessage = ''

      try {
        const result = await this.login(this.username, this.password)

        if (result.success) {
          this.router.push('/dashboard')
        } else {
          this.errorMessage = result.error || 'Login failed'
        }
      } catch (error) {
        this.errorMessage = 'An unexpected error occurred'
      } finally {
        this.isLoading = false
      }
    }
  }
}
  </script>

  <template>
    <h1>Login Page</h1>
    <form @submit.prevent="tryLogin">
      <label for="username">Username:</label>
      <input v-model="username" type="text" id="username" name="username" required :disabled="isLoading">
      <label for="password">Password:</label>
      <input v-model="password" type="password" id="password" name="password" required :disabled="isLoading">

      <div v-if="errorMessage" style="color: red; margin: 10px 0;">
        {{ errorMessage }}
      </div>

      <button @click="tryLogin" type="submit" style="height: 20px;width: 60px" :disabled="isLoading">
        {{ isLoading ? 'Loading...' : 'Login' }}
      </button>
    </form>
  </template>

  <style></style>
