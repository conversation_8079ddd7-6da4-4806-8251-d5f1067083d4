// Simple manual test for authentication flow
// This can be run in the browser console to test the authentication functionality

export function testAuthFlow() {
  console.log('Testing authentication flow...')
  
  // Test 1: Check initial state
  const auth = useAuth()
  console.log('Initial auth state:', auth.isAuthenticated.value)
  
  // Test 2: Test login with empty credentials
  auth.login('', '').then(result => {
    console.log('Empty credentials result:', result)
  })
  
  // Test 3: Test login with valid credentials
  auth.login('testuser', 'testpass').then(result => {
    console.log('Valid credentials result:', result)
    console.log('Auth state after login:', auth.isAuthenticated.value)
    console.log('Current user:', auth.currentUser.value)
  })
  
  // Test 4: Test logout
  setTimeout(() => {
    auth.logout()
    console.log('Auth state after logout:', auth.isAuthenticated.value)
    console.log('Current user after logout:', auth.currentUser.value)
  }, 2000)
}

// Instructions for manual testing:
// 1. Open browser console
// 2. Import this function: import { testAuthFlow } from './tests/auth.test.js'
// 3. Run: testAuthFlow()
