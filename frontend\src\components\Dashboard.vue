<script setup>
import { ref, provide } from 'vue'
import QuadrantCard from './QuadrantCard.vue'
import BaseButton from './ui/BaseButton.vue'
import { useAuth } from '../composables/useAuth.js'
import { useRouter } from 'vue-router'

const { logout, currentUser } = useAuth()
const router = useRouter()

// Global state for selected class
const selectedClass = ref(null)

// Provide the selected class to all child components
provide('selectedClass', selectedClass)

// Function to handle class selection
const selectClass = (classData) => {
  selectedClass.value = classData
}

// Provide the select function to child components
provide('selectClass', selectClass)

// Handle logout
const handleLogout = () => {
  logout()
  router.push('/login')
}
</script>

<template>
  <div class="app-container">
    <!-- Top Navigation Bar -->
    <header class="navbar navbar-expand-lg navbar-dark bg-secondary">
      <div class="container-fluid">
        <div class="navbar-brand">
          <h1 class="h4 mb-0">topicus</h1>
        </div>
        <nav class="d-flex align-items-center">
          <span class="text-light me-3">Welcome, {{ currentUser?.username }}</span>
          <BaseButton
            variant="outline-light"
            size="sm"
            class="me-2"
            icon="bi-bell"
            aria-label="Notifications"
          />
          <BaseButton
            @click="handleLogout"
            variant="outline-light"
            size="sm"
            icon="bi-box-arrow-right"
            aria-label="Logout"
          />
        </nav>
      </div>
    </header>

    <!-- Main Content Area with 4 Quadrants -->
    <main class="container-fluid py-4 bg-light min-vh-100">
      <div class="row g-3">
        <div class="col-md-6">
          <QuadrantCard
            title="Classes"
            add-button-text="Add Class"
            type="classes"
          />
        </div>
        <div class="col-md-6">
          <QuadrantCard
            :title="selectedClass ? `Students - ${selectedClass.name}` : 'Students - Select a class'"
            add-button-text="Add Student"
            type="students"
          />
        </div>
        <div class="col-md-6">
          <QuadrantCard
            :title="selectedClass ? `Documents - ${selectedClass.name}` : 'Documents - Select a class'"
            add-button-text="Add Document"
            type="documents"
          />
        </div>
        <div class="col-md-6">
          <QuadrantCard
            :title="selectedClass ? `Statistics - ${selectedClass.name}` : 'Statistics - Select a class'"
            type="statistics"
          />
        </div>
      </div>
    </main>
  </div>
</template>
